## 0.8.58 - 2024-11-22
### Added
* OpenAI predicted outputs support
* Improve codebase retrieval with BM25
* Support for Grok from xAI
* Chat enhancements including sticking input to bottom
* New UI for cmd+I in sidebar
* Support for Nebius LLM provider
* Support for Ask Sage LLM provider
* Improved reference for config.json
* New @web context provider
* Updates for llama3.2
* .continuerules file to set system prompt
* .prompt files v2
* Dedicated UI for docs indexing
* Clickable code symbols in chat
* Use clipboard content as autocomplete context
### Changed
* Improved @docs crawler
* Many improvements to make autocomplete more eager
* Near complete type definition retrieval for TypeScript autocomplete
* Remove footer from chat sidebar
### Fixed
* Brought back the Apply button for all code blocks
* Automatically update codebase index on removed files
