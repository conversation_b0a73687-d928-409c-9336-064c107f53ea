// Generated by continue

import { getAllSuggestedDocs } from "./index";
import { testIde } from "../../../test/fixtures";
import {
  setUpTestDir,
  tearDownTestDir,
  addToTestDir,
} from "../../../test/testDir";
import fetch, { RequestInfo } from "node-fetch";

jest.mock("node-fetch", undefined, {
  virtual: false,
});

describe.skip("getAllSuggestedDocs", () => {
  beforeEach(() => {
    setUpTestDir();
    jest.clearAllMocks();
  });

  afterEach(() => {
    tearDownTestDir();
    jest.restoreAllMocks();
  });

  it("should return package docs for JavaScript and Python projects", async () => {
    // Set up test files
    addToTestDir([
      [
        "package.json",
        JSON.stringify({
          dependencies: {
            express: "^4.17.1",
            lodash: "^4.17.21",
          },
          devDependencies: {
            typescript: "^4.0.0",
          },
        }),
      ],
      ["requirements.txt", "requests==2.25.1\nflask==1.1.2"],
    ]);

    const mockedFetch = fetch as jest.MockedFunction<typeof fetch>;
    mockedFetch.mockImplementation((url: URL | RequestInfo) => {
      if (url.toString().includes("registry.npmjs.org/express")) {
        return Promise.resolve({
          ok: true,
          json: jest.fn().mockResolvedValue({
            homepage: "https://expressjs.com/",
            name: "express",
            description: "Fast, unopinionated, minimalist web framework",
            license: "MIT",
          }),
        }) as any;
      } else if (url.toString().includes("registry.npmjs.org/lodash")) {
        return Promise.resolve({
          ok: true,
          json: jest.fn().mockResolvedValue({
            homepage: "https://lodash.com/",
            name: "lodash",
            description: "Lodash modular utilities.",
            license: "MIT",
          }),
        }) as any;
      } else if (url.toString().includes("pypi.org/pypi/requests/json")) {
        return Promise.resolve({
          ok: true,
          json: jest.fn().mockResolvedValue({
            info: {
              home_page: "https://docs.python-requests.org/",
              name: "requests",
              summary: "Python HTTP for Humans.",
              license: "Apache 2.0",
            },
          }),
        }) as any;
      } else if (url.toString().includes("pypi.org/pypi/flask/json")) {
        return Promise.resolve({
          ok: true,
          json: jest.fn().mockResolvedValue({
            info: {
              home_page: "https://palletsprojects.com/p/flask/",
              name: "flask",
              summary:
                "A simple framework for building complex web applications.",
              license: "BSD-3-Clause",
            },
          }),
        }) as any;
      } else {
        return Promise.reject(new Error(`Unhandled URL: ${url}`));
      }
    });

    const results = await getAllSuggestedDocs(testIde);

    expect(results).toHaveLength(4);
    expect(results).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          packageInfo: expect.objectContaining({ name: "express" }),
          details: expect.objectContaining({
            docsLink: "https://expressjs.com/",
          }),
        }),
        expect.objectContaining({
          packageInfo: expect.objectContaining({ name: "lodash" }),
          details: expect.objectContaining({
            docsLink: "https://lodash.com/",
          }),
        }),
        expect.objectContaining({
          packageInfo: expect.objectContaining({ name: "requests" }),
          details: expect.objectContaining({
            docsLink: "https://docs.python-requests.org/",
          }),
        }),
        expect.objectContaining({
          packageInfo: expect.objectContaining({ name: "flask" }),
          details: expect.objectContaining({
            docsLink: "https://palletsprojects.com/p/flask/",
          }),
        }),
      ]),
    );

    // Verify no errors are present
    results.forEach((result) => {
      expect(result.error).toBeUndefined();
    });
  });

  it("should handle packages without documentation links", async () => {
    addToTestDir([
      [
        "package.json",
        JSON.stringify({
          dependencies: {
            "no-docs-package": "^1.0.0",
          },
        }),
      ],
    ]);

    // Mock fetch
    const mockedFetch = fetch as jest.MockedFunction<typeof fetch>;
    mockedFetch.mockImplementation((url: RequestInfo | URL) => {
      if (url.toString().includes("registry.npmjs.org/no-docs-package")) {
        return Promise.resolve({
          ok: true,
          json: jest.fn().mockResolvedValue({
            name: "no-docs-package",
            description: "A package without docs",
            license: "MIT",
            // No homepage provided
          }),
        }) as any;
      } else {
        return Promise.reject(new Error(`Unhandled URL: ${url}`));
      }
    });

    const results = await getAllSuggestedDocs(testIde);

    expect(results).toHaveLength(1);
    expect(results[0].error).toContain(
      "No documentation link found for no-docs-package",
    );
    expect(results[0].details).toBeUndefined();
  });

  it("should handle errors when fetching package details", async () => {
    addToTestDir([
      [
        "package.json",
        JSON.stringify({
          dependencies: {
            "error-package": "^1.0.0",
          },
        }),
      ],
    ]);

    // Mock fetch
    const mockedFetch = fetch as jest.MockedFunction<typeof fetch>;
    mockedFetch.mockImplementation((url: URL | RequestInfo) => {
      if (url.toString().includes("registry.npmjs.org/error-package")) {
        return Promise.resolve({
          ok: false,
        }) as any;
      } else {
        return Promise.reject(new Error(`Unhandled URL: ${url}`));
      }
    });

    const results = await getAllSuggestedDocs(testIde);

    expect(results).toHaveLength(1);
    expect(results[0].error).toContain(
      "Error getting package details for error-package",
    );
    expect(results[0].details).toBeUndefined();
  });

  it("should handle workspaces with no package files", async () => {
    const results = await getAllSuggestedDocs(testIde);

    expect(results).toEqual([]);
  });

  it("should handle packages with GitHub documentation links", async () => {
    addToTestDir([
      [
        "package.json",
        JSON.stringify({
          dependencies: {
            "github-docs-package": "^1.0.0",
          },
        }),
      ],
    ]);

    // Mock fetch
    const mockedFetch = fetch as jest.MockedFunction<typeof fetch>;
    mockedFetch.mockImplementation((url: URL | RequestInfo) => {
      if (url.toString().includes("registry.npmjs.org/github-docs-package")) {
        return Promise.resolve({
          ok: true,
          json: jest.fn().mockResolvedValue({
            homepage: "https://github.com/user/repo",
            name: "github-docs-package",
            description: "A package with GitHub docs",
            license: "MIT",
          }),
        }) as any;
      } else {
        return Promise.reject(new Error(`Unhandled URL: ${url}`));
      }
    });

    const results = await getAllSuggestedDocs(testIde);

    expect(results).toHaveLength(1);
    expect(results[0].details?.docsLink).toBe("https://github.com/user/repo");
    expect(results[0].details?.docsLinkWarning).toBe(
      "Github docs not supported, find the docs site",
    );
  });

  it("should handle packages with non-docs links", async () => {
    addToTestDir([["requirements.txt", "non-docs-package==1.0.0"]]);

    // Mock fetch
    const mockedFetch = fetch as jest.MockedFunction<typeof fetch>;
    mockedFetch.mockImplementation((url: URL | RequestInfo) => {
      if (url.toString().includes("pypi.org/pypi/non-docs-package/json")) {
        return Promise.resolve({
          ok: true,
          json: jest.fn().mockResolvedValue({
            info: {
              home_page: "https://somewebsite.com",
              name: "non-docs-package",
              summary: "A package with non-docs homepage",
              license: "MIT",
            },
          }),
        }) as any;
      } else {
        return Promise.reject(new Error(`Unhandled URL: ${url}`));
      }
    });

    const results = await getAllSuggestedDocs(testIde);

    expect(results).toHaveLength(1);
    expect(results[0].details?.docsLink).toBe("https://somewebsite.com");
    expect(results[0].details?.docsLinkWarning).toBe(
      "May not be a docs site, check the URL",
    );
  });
});
