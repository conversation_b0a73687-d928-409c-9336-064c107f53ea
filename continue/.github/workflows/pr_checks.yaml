name: PR checks

on:
  pull_request:
    branches:
      - main
    paths:
      - "extensions/vscode/**"
      - "core/**"
      - "gui/**"
      - ".github/workflows/**"

  push:
    branches:
      - main
    paths:
      - "extensions/intellij/**"
      - "extensions/vscode/**"
      - "core/**"
      - "gui/**"
      - ".github/workflows/**"

jobs:
  install-root:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: root-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - name: Install root dependencies
        if: steps.root-cache.outputs.cache-hit != 'true'
        run: npm ci

  install-core:
    needs: install-root
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: root-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        id: core-cache
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - name: Install core dependencies
        if: steps.core-cache.outputs.cache-hit != 'true'
        run: |
          cd core
          npm ci

  core-checks:
    needs: install-core
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: root-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        id: core-cache
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - name: Type check and lint
        run: |
          cd core
          npx tsc --noEmit
          npm run lint
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

  install-gui:
    needs: [ install-root, install-core ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: gui-cache
        with:
          path: gui/node_modules
          key: ${{ runner.os }}-gui-node-modules-${{ hashFiles('gui/package-lock.json') }}

      - name: Install gui dependencies
        if: steps.gui-cache.outputs.cache-hit != 'true'
        run: |
          cd gui
          npm ci

  gui-checks:
    needs: install-gui
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: gui-cache
        with:
          path: gui/node_modules
          key: ${{ runner.os }}-gui-node-modules-${{ hashFiles('gui/package-lock.json') }}

      - name: Type check
        run: |
          cd gui
          npx tsc --noEmit

  binary-checks:
    needs: [ install-root, install-core ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: binary-cache
        with:
          path: binary/node_modules
          key: ${{ runner.os }}-binary-node-modules-${{ hashFiles('binary/package-lock.json') }}

      - name: Install binary dependencies
        if: steps.binary-cache.outputs.cache-hit != 'true'
        run: |
          cd binary
          npm ci

      - name: Type check
        run: |
          cd binary
          npx tsc --noEmit

  install-vscode:
    needs: [ install-root, install-core ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: vscode-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - name: Install vscode dependencies
        if: steps.vscode-cache.outputs.cache-hit != 'true'
        run: |
          cd extensions/vscode
          npm ci
        env:
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

  vscode-checks:
    needs: install-vscode
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-root-node-modules-${{ hashFiles('package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - uses: actions/cache@v4
        id: vscode-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - name: Type check and lint
        run: |
          cd extensions/vscode
          npx tsc --noEmit
          npm run lint

  core-tests:
    needs: install-core
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - name: Run core tests
        run: |
          cd core
          npm test

  vscode-get-test-file-matrix:
    runs-on: ubuntu-latest
    needs: [ install-root, install-vscode ]
    outputs:
      test_file_matrix: ${{ steps.vscode-get-test-file-matrix.outputs.test_file_matrix }}
    steps:
      - uses: actions/checkout@v4

      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - name: Get test files
        id: vscode-get-test-file-matrix
        run: |
          cd extensions/vscode
          npm ci
          npm run e2e:compile
          FILES=$(ls -1 e2e/_output/tests/*.test.js | jq -R . | jq -s .)
          echo "test_file_matrix<<EOF" >> $GITHUB_OUTPUT
          echo "$FILES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Debug Outputs
        run: |
          echo "Test files: ${{ steps.vscode-get-test-file-matrix.outputs.test_file_matrix }}"

  vscode-package-extension:
    runs-on: ubuntu-latest
    needs: [ install-vscode, install-core ]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: vscode-node-modules-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - uses: actions/cache@v4
        id: core-node-modules-cache
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - name: Package extension
        run: |
          cd extensions/vscode
          npm run package

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: vscode-extension-build
          path: extensions/vscode/build

  vscode-download-e2e-dependencies:
    runs-on: ubuntu-latest
    needs: [ install-vscode, install-core ]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: vscode-node-modules-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - uses: actions/cache@v4
        id: storage-cache
        with:
          path: extensions/vscode/e2e/storage
          key: ${{ runner.os }}-vscode-storage-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - name: Download Dependencies
        if: steps.storage-cache.outputs.cache-hit != 'true'
        run: |
          cd extensions/vscode
          npm run e2e:ci:download

      - name: Upload e2e dependencies
        uses: actions/upload-artifact@v4
        with:
          name: vscode-e2e-dependencies
          path: extensions/vscode/e2e/storage

  vscode-e2e-tests:
    name: ${{ matrix.test_file }}"
    needs:
      [
        vscode-download-e2e-dependencies,
        vscode-get-test-file-matrix,
        vscode-package-extension,
        install-vscode,
        install-core,
      ]
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        test_file: ${{ fromJson(needs.vscode-get-test-file-matrix.outputs.test_file_matrix) }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: vscode-node-modules-cache
        with:
          path: extensions/vscode/node_modules
          key: ${{ runner.os }}-vscode-node-modules-${{ hashFiles('extensions/vscode/package-lock.json') }}

      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: vscode-extension-build
          path: extensions/vscode/build

      - name: Download e2e dependencies
        uses: actions/download-artifact@v4
        with:
          name: vscode-e2e-dependencies
          path: extensions/vscode/e2e/storage

      - name: Fix VSCode binary permissions
        run: |
          chmod +x extensions/vscode/e2e/storage/VSCode-linux-x64/code
          chmod +x extensions/vscode/e2e/storage/chromedriver-linux64/chromedriver

      - name: Set up SSH
        env:
          SSH_KEY: ${{ secrets.GH_ACTIONS_SSH_TEST_KEY_PEM }}
          SSH_HOST: ${{ secrets.GH_ACTIONS_SSH_TEST_DNS_NAME }}
        run: |
          mkdir -p ~/.ssh
          echo "$SSH_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H "$SSH_HOST" >> ~/.ssh/known_hosts
          echo -e "Host ssh-test-container\n\tHostName $SSH_HOST\n\tUser ec2-user\n\tIdentityFile ~/.ssh/id_rsa" >> ~/.ssh/config

      - name: Set up Xvfb
        run: |
          Xvfb :99 &
          export DISPLAY=:99

      - name: Run e2e tests
        run: |
          cd extensions/vscode
          TEST_FILE="${{ matrix.test_file }}" npm run e2e:ci:run

        env:
          DISPLAY: :99

      - name: Upload e2e test screenshots
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-failure-screenshots
          path: extensions/vscode/e2e/storage/screenshots

  gui-tests:
    needs: [ install-gui, install-core ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: gui-cache
        with:
          path: gui/node_modules
          key: ${{ runner.os }}-gui-node-modules-${{ hashFiles('gui/package-lock.json') }}

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - name: Install GUI dependencies
        if: steps.gui-cache.outputs.cache-hit != 'true'
        run: cd gui && npm ci
        env:
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

      - name: Run gui tests
        run: |
          cd gui
          npm test

  jetbrains-tests:
    needs: [ install-root, core-checks ]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/cache@v4
        with:
          path: core/node_modules
          key: ${{ runner.os }}-core-node-modules-${{ hashFiles('core/package-lock.json') }}

      - name: Setup Java
        uses: actions/setup-java@v4.5.0
        with:
          distribution: zulu
          java-version: 17

      - name: Setup FFmpeg
        uses: AnimMouse/setup-ffmpeg@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v3

      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - uses: actions/cache@v4
        id: gui-cache
        with:
          path: gui/node_modules
          key: ${{ runner.os }}-gui-node-modules-${{ hashFiles('gui/package-lock.json') }}

      # We can shave off another minute off our CI script by finding a way to share this with vscode-tests
      - name: Run prepackage script
        run: |
          cd extensions/vscode
          npm ci
          npm run prepackage
        env:
          # https://github.com/microsoft/vscode-ripgrep/issues/9#issuecomment-643965333
          GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}

      - uses: actions/cache@v4
        id: binary-cache
        with:
          path: binary/node_modules
          key: ${{ runner.os }}-binary-node-modules-${{ hashFiles('binary/package-lock.json') }}

      - name: Build the binaries
        run: |
          cd binary
          npm run build

      - name: Start test IDE
        run: |
          cd extensions/intellij
          export DISPLAY=:99.0
          Xvfb -ac :99 -screen 0 1920x1080x24 &
          sleep 10
          mkdir -p build/reports
          ./gradlew runIdeForUiTests &

      - name: Wait for JB connection
        uses: jtalk/url-health-check-action@v3
        with:
          url: http://127.0.0.1:8082
          max-attempts: 15
          retry-delay: 5s

      - name: Run tests
        run: |
          cd extensions/intellij
          export DISPLAY=:99.0
          ./gradlew test

      - name: Move video
        if: ${{ failure() }}
        run: |
          cd extensions/intellij
          mv video build/reports

      - name: Copy logs
        if: ${{ failure() }}
        run: |
          cd extensions/intellij
          mv build/idea-sandbox/system/log/ build/reports

      - name: Save fails report
        if: ${{ failure() }}
        uses: actions/upload-artifact@v4
        with:
          name: jb-failure-report
          path: |
            ${{ github.workspace }}/extensions/intellij/build/reports


