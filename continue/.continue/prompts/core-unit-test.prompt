name: Write Core Unit Test
description: Generate unit tests for core utilities
---
Write jest tests for the provided code.
Use jest version ^29 (e.g. jest 29.7.0)

Use best practices. Be clear and consise.
Aim for 100% code coverage where reasonable.
Multiple tests can be written, split up tests for best clarity and readability.
Only use typescript, and if the file/code is not typescript, warn the user.
IMPORTANT Use ESM to import modules, do NOT use `require` anywhere
Tests are to be described in an adjacent file with a path identical except for a `.test.ts` rather than a `.ts` file extension
Use double quotes (or backticks if needed) for strings

The code being tested is used in IDE extensions, and it:
- accesses code workspaces through the IDE ("workspace directories")
- persists extension-related data to the the local machine of the user ("global directory"), and
- uses configuration via a `ConfigHandler`, which is stored in either the global directory (default) or accesed via a remote "control plane" using the `ControlPlaneClient`

Jest testing setup includes
- @core/test/jest.global-setup.ts initializes a temporary global directory, which is where files that store persisted extension data live.
- @core/test/testDir.ts provides utils for creating and working with the temporary workspace directory. Use `setUpTestDir` and `tearDownTestDir` explicitly in tests that work with workspace files
- @core/test/jest.setup-after-env.ts gives tests access to node and jest globals

@core/test/fixtures.ts provides fixtures that should be used in tests to emulate extension behavior
- import `testIde` for IDE/workspace operations
- import `testConfigHandler` for any ConfigHandler needs
- import `testControlPlaneClient` for control plane operations
- import `ideSettingsPromise` for any IdeSettings needs
- import `testLLM` for any ILLM/BaseLLM needs. Set the `completion` property to the desired completion, e.g. `testLLM.completion = "Desired completion";`

Do NOT write tests for any files in `core/test`, only use them as helpers for testing other files. If no other files are provided, warn the user and write no tests.

IMPORTANT: Do NOT mock the fixtures above other than using `jest.spyOn`. DO mock 3rd party modules, etc. when sensible. 
Instead, generate actual mock files and data for operations
Pure mocks should only be used to emulate specific network responses/error or hard-to-duplicate errors, or to prevent long-duration tests

Additional types can be imported from @core/index.d.ts
If any needed types, functions, constants, or classes are still not found, warn the user and do not generate tests.

Write the comment "// Generated by continue" at the top of the generated code/file (not the filepath)